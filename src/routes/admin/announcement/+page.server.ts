import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { announcements } from '$lib/server/db/schemas/artworks/announcement';
import { subscriberUser } from '$lib/server/db/schemas/users/subscriber';
import { artRegistrations } from '$lib/server/db/schemas/artworks/registrations';
import { eq } from 'drizzle-orm';
import {
  GENDER,
  GOVERNORATES,
  EDUCATION_LEVELS,
  REFERRAL_SOURCES,
  PAYMENT_PREFERENCE,
  YES_NO,
  SPECIALIZATION,
  announcementSchema
} from '@/routes/zod-schemas';
import { zod } from 'sveltekit-superforms/adapters';
import { message, superValidate } from 'sveltekit-superforms/server';

export const load: PageServerLoad = async () => {
  const allAnnouncements = await db.select().from(announcements).orderBy(announcements.createdAt);

  // Get all subscriber users with their related art registration data (if exists)
  const allSubscriberUsers = await db
    .select({
      id: subscriberUser.id,
      phoneNumber: subscriberUser.phoneNumber,
      createdAt: subscriberUser.createdAt,
      fullName: artRegistrations.fullName
    })
    .from(subscriberUser)
    .leftJoin(
      artRegistrations,
      eq(subscriberUser.id, artRegistrations.userId)
    )
    .orderBy(subscriberUser.createdAt);

  return {
    announcements: allAnnouncements,
    subscriberUsers: allSubscriberUsers,
    enums: {
      GENDER,
      GOVERNORATES,
      EDUCATION_LEVELS,
      REFERRAL_SOURCES,
      PAYMENT_PREFERENCE,
      YES_NO,
      SPECIALIZATION
    }
  };
};

export const actions: Actions = {
  createAnnouncement: async ({ request }) => {
    const formData = await request.formData();

    const title = formData.get('title')?.toString();
    const content = formData.get('content')?.toString();
    const userId = formData.get('userId')?.toString();

    // Get targeting fields from form data
    const targetGender = formData.get('targetGender')?.toString();
    const targetGovernorate = formData.get('targetGovernorate')?.toString();
    const targetEducationLevel = formData.get('targetEducationLevel')?.toString();
    const targetReferralSource = formData.get('targetReferralSource')?.toString();
    const targetDepartment = formData.get('targetDepartment')?.toString();
    const targetSpecialization = formData.get('targetSpecialization')?.toString();
    const targetHasPreviousExperience = formData.get('targetHasPreviousExperience')?.toString();
    const targetKnowsFineArtMarket = formData.get('targetKnowsFineArtMarket')?.toString();
    const targetWatchedRikabiEpisodes = formData.get('targetWatchedRikabiEpisodes')?.toString();
    const targetAdoptsRikabiPrinciples = formData.get('targetAdoptsRikabiPrinciples')?.toString();
    const targetReadyForYearCommitment = formData.get('targetReadyForYearCommitment')?.toString();
    const targetPaymentPreference = formData.get('targetPaymentPreference')?.toString();
    const targetReadyForOnSiteTraining = formData.get('targetReadyForOnSiteTraining')?.toString();

    if (!title || !content) {
      return fail(400, { error: 'Title and content are required' });
    }

    try {
      await db.insert(announcements).values({
        title,
        content,
        userId: userId || undefined,
        targetGender: targetGender || undefined,
        targetGovernorate: targetGovernorate || undefined,
        targetEducationLevel: targetEducationLevel || undefined,
        targetReferralSource: targetReferralSource || undefined,
        targetDepartment: targetDepartment || undefined,
        targetSpecialization: targetSpecialization || undefined,
        targetHasPreviousExperience: targetHasPreviousExperience || undefined,
        targetKnowsFineArtMarket: targetKnowsFineArtMarket || undefined,
        targetWatchedRikabiEpisodes: targetWatchedRikabiEpisodes || undefined,
        targetAdoptsRikabiPrinciples: targetAdoptsRikabiPrinciples || undefined,
        targetReadyForYearCommitment: targetReadyForYearCommitment || undefined,
        targetPaymentPreference: targetPaymentPreference || undefined,
        targetReadyForOnSiteTraining: targetReadyForOnSiteTraining || undefined
      });

      return { success: true };
    } catch (error) {
      console.error('Error creating announcement:', error);
      return fail(500, { error: 'Failed to create announcement' });
    }
  },

  deleteAnnouncement: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id')?.toString();

    if (!id) {
      return fail(400, { error: 'Announcement ID is required' });
    }

    try {
      await db.delete(announcements).where(eq(announcements.id, id));
      return { success: true };
    } catch (error) {
      console.error('Error deleting announcement:', error);
      return fail(500, { error: 'Failed to delete announcement' });
    }
  }
};
