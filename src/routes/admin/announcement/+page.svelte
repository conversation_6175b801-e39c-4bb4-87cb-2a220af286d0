<script lang="ts">
 import { enhance } from '$app/forms';
 import type { PageData } from './$types';
 import { Button, buttonVariants } from '$lib/components/ui/button';
 import { Input } from '$lib/components/ui/input';
 import { Textarea } from '$lib/components/ui/textarea';
 import { Label } from '$lib/components/ui/label';
 import * as Table from '$lib/components/ui/table';
 import * as Tabs from '$lib/components/ui/tabs';
 import * as Select from '$lib/components/ui/select';
 import * as Command from '$lib/components/ui/command';
 import * as Popover from '$lib/components/ui/popover';
 import { cn } from '$lib/utils';
 import { tick } from 'svelte';
 import CheckIcon from '@lucide/svelte/icons/check';
 import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
 import type { SubscriberUser } from '$lib/server/db/schemas/users/subscriber'; // Assuming this path is correct

 let { data } = $props();
 let showTargetingOptions = $state(false);
 let selectedUserId = $state('');
 let popoverOpen = $state(false);
 let triggerRef = $state<HTMLButtonElement>(null!);

 function toggleTargetingOptions() {
 	showTargetingOptions = !showTargetingOptions;
 }

 function formatDate(timestamp: number) {
 	return new Date(timestamp * 1000).toLocaleString('ar-IQ');
 }

 const selectedUser = $derived(
 	data.subscriberUsers.find((user: SubscriberUser) => user.id === selectedUserId)
 );

 // We want to refocus the trigger button when the user selects
 // an item from the list so users can continue navigating the
 // rest of the form with the keyboard.
 function closeAndFocusTrigger() {
 	popoverOpen = false;
 	tick().then(() => {
 		triggerRef.focus();
 	});
 }
</script>

<div class="container mx-auto p-4">
	<h1 class="text-2xl font-bold mb-6">إدارة الإعلانات</h1>

	<Tabs.Root value="create" class="w-full">
		<Tabs.List class="grid w-full grid-cols-2 mb-6">
			<Tabs.Trigger value="create">إنشاء إعلان جديد</Tabs.Trigger>
			<Tabs.Trigger value="list">قائمة الإعلانات ({data.announcements.length})</Tabs.Trigger>
		</Tabs.List>

		<Tabs.Content value="create">
		<div class="bg-white p-6 rounded-lg shadow-md">
			<h2 class="text-xl font-semibold mb-4">إنشاء إعلان جديد</h2>

			<form method="POST" action="?/createAnnouncement" use:enhance>
				<div class="grid gap-4 mb-4">
					<div class="grid gap-2">
						<Label for="title">عنوان الإعلان</Label>
						<Input
							type="text"
							id="title"
							name="title"
							required
						/>
					</div>

					<div class="grid gap-2">
						<Label for="content">محتوى الإعلان</Label>
						<Textarea
							id="content"
							name="content"
							rows={4}
							required
						/>
					</div>

					<div class="grid gap-2">
						<Label for="userId">المستخدم (اختياري)</Label>
						<Popover.Root bind:open={popoverOpen}>
							<Popover.Trigger bind:ref={triggerRef}>
								{#snippet child({ props })}
									<Button
										variant="outline"
										role="combobox"
										aria-expanded={popoverOpen}
										class="w-full justify-between"
										{...props}
									>
										{selectedUser
											? selectedUser.fullName
												? `${selectedUser.phoneNumber} - ${selectedUser.fullName}`
												: selectedUser.phoneNumber
											: 'اختر مستخدم'}
										<ChevronsUpDownIcon class="ml-2 h-4 w-4 shrink-0 opacity-50" />
									</Button>
								{/snippet}
							</Popover.Trigger>
							<Popover.Content class="w-[--radix-popover-trigger-width] max-h-[300px] p-0" dir="rtl">
								<Command.Root>
									<Command.Input
										placeholder="ابحث عن مستخدم برقم الهاتف أو الاسم الكامل..."
										class="h-9"
									/>
									<Command.List>
										<Command.Empty>لم يتم العثور على نتائج</Command.Empty>
										<Command.Group>
											{#each data.subscriberUsers || [] as user (user.id)}
												<Command.Item
													value={user.phoneNumber + ' ' + (user.fullName || '')}
													onSelect={() => {
														selectedUserId = selectedUserId === user.id ? '' : user.id;
														closeAndFocusTrigger();
													}}
													class="flex justify-between"
												>
													<span>{user.phoneNumber}</span>
													<span class="text-muted-foreground text-xs">{user.fullName || 'بدون اسم'}</span>
													<CheckIcon
														class={cn(
															"ml-2 h-4 w-4",
															selectedUserId !== user.id && "text-transparent"
														)}
													/>
												</Command.Item>
											{/each}
										</Command.Group>
									</Command.List>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<input type="hidden" name="userId" value={selectedUserId} />
						{#if selectedUser}
							<div class="mt-2 text-sm bg-blue-50 p-2 rounded flex justify-between items-center">
								<span>تم اختيار: {selectedUser.fullName ? `${selectedUser.phoneNumber} - ${selectedUser.fullName}` : selectedUser.phoneNumber}</span>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									class="h-6 w-6 p-0"
									onclick={() => {
										selectedUserId = '';
									}}
								>
									✕
								</Button>
							</div>
						{/if}
					</div>

					<div>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onclick={toggleTargetingOptions}
						>
							{showTargetingOptions ? 'إخفاء خيارات الاستهداف' : 'عرض خيارات الاستهداف'}
						</Button>
					</div>
				</div>

				{#if showTargetingOptions}
					<div class="border p-4 rounded mb-4">
						<h3 class="font-medium mb-3">خيارات الاستهداف</h3>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<!-- Gender targeting -->
							<div class="grid gap-2">
								<Label for="targetGender">الجنس</Label>
								<Select.Root type="single" name="targetGender">
									<Select.Trigger id="targetGender" class="w-full">
										الجنس
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.GENDER as gender (gender)}
											<Select.Item value={gender}>{gender}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Governorate targeting -->
							<div class="grid gap-2">
								<Label for="targetGovernorate">المحافظة</Label>
								<Select.Root type="single" name="targetGovernorate">
									<Select.Trigger id="targetGovernorate" class="w-full">
										المحافظة
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.GOVERNORATES as governorate (governorate)}
											<Select.Item value={governorate}>{governorate}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Education Level targeting -->
							<div class="grid gap-2">
								<Label for="targetEducationLevel">المستوى التعليمي</Label>
								<Select.Root type="single" name="targetEducationLevel">
									<Select.Trigger id="targetEducationLevel" class="w-full">
										المستوى التعليمي
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.EDUCATION_LEVELS as level (level)}
											<Select.Item value={level}>{level}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Referral Source targeting -->
							<div class="grid gap-2">
								<Label for="targetReferralSource">مصدر التعرف</Label>
								<Select.Root type="single" name="targetReferralSource">
									<Select.Trigger id="targetReferralSource" class="w-full">
										مصدر التعرف
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.REFERRAL_SOURCES as source (source)}
											<Select.Item value={source}>{source}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Department targeting -->
							<div class="grid gap-2">
								<Label for="targetDepartment">القسم</Label>
								<Select.Root type="single" name="targetDepartment">
									<Select.Trigger id="targetDepartment" class="w-full">
										القسم
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.SPECIALIZATION as specialization (specialization)}
											<Select.Item value={specialization}>{specialization}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Specialization targeting -->
							<div class="grid gap-2">
								<Label for="targetSpecialization">التخصص</Label>
								<Select.Root type="single" name="targetSpecialization">
									<Select.Trigger id="targetSpecialization" class="w-full">
										التخصص
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.SPECIALIZATION as specialization (specialization)}
											<Select.Item value={specialization}>{specialization}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Yes/No fields targeting -->
							<div class="grid gap-2">
								<Label for="targetHasPreviousExperience">لديه خبرة سابقة</Label>
								<Select.Root type="single" name="targetHasPreviousExperience">
									<Select.Trigger id="targetHasPreviousExperience" class="w-full">
										الخبرة السابقة
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid gap-2">
								<Label for="targetKnowsFineArtMarket">يعرف سوق الفنون</Label>
								<Select.Root type="single" name="targetKnowsFineArtMarket">
									<Select.Trigger id="targetKnowsFineArtMarket" class="w-full">
										السوق
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid gap-2">
								<Label for="targetWatchedRikabiEpisodes">شاهد حلقات الريكابي</Label>
								<Select.Root type="single" name="targetWatchedRikabiEpisodes">
									<Select.Trigger id="targetWatchedRikabiEpisodes" class="w-full">
										الحلقات
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid gap-2">
								<Label for="targetAdoptsRikabiPrinciples">يتبنى مبادئ الريكابي</Label>
								<Select.Root type="single" name="targetAdoptsRikabiPrinciples">
									<Select.Trigger id="targetAdoptsRikabiPrinciples" class="w-full">
										المبادئ
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid gap-2">
								<Label for="targetReadyForYearCommitment">مستعد للالتزام لمدة سنة</Label>
								<Select.Root type="single" name="targetReadyForYearCommitment">
									<Select.Trigger id="targetReadyForYearCommitment" class="w-full">
										الالتزام
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid gap-2">
								<Label for="targetReadyForOnSiteTraining">مستعد للتدريب الحضوري</Label>
								<Select.Root type="single" name="targetReadyForOnSiteTraining">
									<Select.Trigger id="targetReadyForOnSiteTraining" class="w-full">
										التدريب
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.YES_NO as option (option)}
											<Select.Item value={option}>{option}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>

							<!-- Payment Preference targeting -->
							<div class="grid gap-2">
								<Label for="targetPaymentPreference">تفضيل الدفع</Label>
								<Select.Root type="single" name="targetPaymentPreference">
									<Select.Trigger id="targetPaymentPreference" class="w-full">
										الدفع
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Item value="">-- اختر --</Select.Item>
										{#each data.enums.PAYMENT_PREFERENCE as preference (preference)}
											<Select.Item value={preference}>{preference}</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>
							</div>
						</div>
					</div>
				{/if}

				<Button type="submit" class="mt-4">
					إنشاء الإعلان
				</Button>
			</form>
		</div>
		</Tabs.Content>

		<Tabs.Content value="list">
			<div class="bg-white p-6 rounded-lg shadow-md">
				<h2 class="text-xl font-semibold mb-4">قائمة الإعلانات</h2>

				{#if data.announcements.length === 0}
					<p class="text-gray-500">لا توجد إعلانات حالياً</p>
				{:else}
					<div class="rounded-md border">
						<Table.Root>
							<Table.Header>
								<Table.Row>
									<Table.Head>العنوان</Table.Head>
									<Table.Head>المحتوى</Table.Head>
									<Table.Head>تاريخ الإنشاء</Table.Head>
									<Table.Head>معايير الاستهداف</Table.Head>
									<Table.Head class="text-right">الإجراءات</Table.Head>
								</Table.Row>
							</Table.Header>
							<Table.Body>
								{#each data.announcements as announcement (announcement.id)}
									<Table.Row>
										<Table.Cell>{announcement.title}</Table.Cell>
										<Table.Cell class="max-w-xs truncate">{announcement.content}</Table.Cell>
										<Table.Cell>{formatDate(announcement.createdAt.getTime() / 1000)}</Table.Cell>
										<Table.Cell>
											<div class="flex flex-wrap gap-1">
												{#if announcement.userId}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">معرف المستخدم</span>
												{/if}
												{#if announcement.targetGender}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">الجنس</span>
												{/if}
												{#if announcement.targetGovernorate}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">المحافظة</span>
												{/if}
												{#if announcement.targetEducationLevel}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">المستوى التعليمي</span>
												{/if}
												{#if announcement.targetReferralSource}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">مصدر التعرف</span>
												{/if}
												{#if announcement.targetDepartment}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">القسم</span>
												{/if}
												{#if announcement.targetSpecialization}
													<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">التخصص</span>
												{/if}
											</div>
										</Table.Cell>
										<Table.Cell class="text-right">
											<form method="POST" action="?/deleteAnnouncement" use:enhance>
												<input type="hidden" name="id" value={announcement.id} />
												<Button type="submit" variant="destructive" size="sm">حذف</Button>
											</form>
										</Table.Cell>
									</Table.Row>
								{/each}
							</Table.Body>
						</Table.Root>
					</div>
				{/if}
			</div>
		</Tabs.Content>
	</Tabs.Root>
</div>
