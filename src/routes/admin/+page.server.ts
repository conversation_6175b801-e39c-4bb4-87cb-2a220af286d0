import type { Actions, PageServerLoad } from './$types';
import { requireAdminLogin } from '$lib/server/login-required';
import { db } from '$lib/server/db';
import { artRegistrations } from '$lib/server/db/schema';
import { subscriberUser } from '$lib/server/db/schemas/users/subscriber';
import { eq, count, sql, or, like, and, SQL, inArray as in_ } from 'drizzle-orm';
import { fail } from '@sveltejs/kit';
import {
	GOVERNORATES,
	EDUCATION_LEVELS,
	REFERRAL_SOURCES,
	YES_NO,
	GENDER,
	PAYMENT_PREFERENCE,
	SPECIALIZATION
} from '../zod-schemas';

export const load: PageServerLoad = async ({ url }) => {
	const user = await requireAdminLogin();

	// Pagination
	const page = parseInt(url.searchParams.get('page') || '1');
	const limit = 10;
	const offset = (page - 1) * limit;

	// Filters
	const search = url.searchParams.get('search') || '';
	const gender = url.searchParams.get('gender') || '';
	const governorate = url.searchParams.get('governorate') || '';
	const educationLevel = url.searchParams.get('educationLevel') || '';
	const department = url.searchParams.get('department') || '';
	const specialization = url.searchParams.get('specialization') || '';

	const whereConditions: SQL[] = [];

	// Apply search
	if (search) {
		const searchTerm = `%${search.toLowerCase()}%`;
		const searchCondition = or(
			like(
				sql`LOWER(
			${artRegistrations.fullName}
			)`,
				searchTerm
			),
			like(
				sql`LOWER(
			${artRegistrations.email}
			)`,
				searchTerm
			),
			like(
				sql`LOWER(
			${subscriberUser.phoneNumber}
			)`,
				searchTerm
			)
		);
		if (searchCondition) {
			whereConditions.push(searchCondition);
		}
	}

	// Apply enum filters
	if (gender) {
		whereConditions.push(eq(artRegistrations.gender, gender));
	}

	if (governorate) {
		whereConditions.push(eq(artRegistrations.governorate, governorate));
	}

	if (educationLevel) {
		whereConditions.push(eq(artRegistrations.educationLevel, educationLevel));
	}
	if (department) {
		whereConditions.push(eq(artRegistrations.department, department));
	}
	if (specialization) {
		whereConditions.push(eq(artRegistrations.specialization, specialization));
	}

	const finalWhereCondition = whereConditions.length > 0 ? and(...whereConditions) : undefined;

	// Get total count for pagination
	const totalCountResult = await db
		.select({ count: count() })
		.from(artRegistrations)
		.leftJoin(subscriberUser, eq(artRegistrations.userId, subscriberUser.id))
		.where(finalWhereCondition);

	const totalCount = totalCountResult[0]?.count || 0;

	// Apply pagination and select all fields
	const registrations = await db
		.select({
			registration: artRegistrations,
			phoneNumber: subscriberUser.phoneNumber
		})
		.from(artRegistrations)
		.leftJoin(subscriberUser, eq(artRegistrations.userId, subscriberUser.id))
		.where(finalWhereCondition)
		.orderBy(
			sql`${artRegistrations.createdAt}
		DESC`
		) // Order by creation date, descending
		.limit(limit)
		.offset(offset);

	// Transform the results to maintain compatibility with the frontend
	const transformedRegistrations = registrations.map(item => {
		// Create a new object with all properties from the registration
		const registrationWithPhone = { ...item.registration };
		// Add the phoneNumber property
		registrationWithPhone.phoneNumber = item.phoneNumber;
		return registrationWithPhone;
	});

	return {
		user,
		registrations: transformedRegistrations,
		pagination: {
			currentPage: page,
			totalPages: Math.ceil(totalCount / limit) || 1, // Ensure totalPages is at least 1
			totalCount
		},
		filters: {
			search,
			gender,
			governorate,
			educationLevel,
			department,
			specialization
		},
		// Add enum options for the dropdowns
		enumOptions: {
			gender: GENDER,
			governorate: GOVERNORATES,
			educationLevel: EDUCATION_LEVELS,
			department: SPECIALIZATION,
			specialization: SPECIALIZATION
		}
	};
};

export const actions: Actions = {
	delete: async ({ request }) => {
		const formData = await request.formData();
		const id = formData.get('id');
		if (id) {
			await db.delete(artRegistrations).where(eq(artRegistrations.id, String(id)));
		} else {
			return fail(400, { success: false, message: 'No ID provided.' });
		}
		return { success: true };
	},

	importData: async ({ request }) => {
		await requireAdminLogin();

		const formData = await request.formData();
		const file = formData.get('file') as File;

		if (!file) {
			return fail(400, { success: false, message: 'No file provided.' });
		}

		// Check a file type
		if (!file.name.endsWith('.csv')) {
			return fail(400, { success: false, message: 'Only CSV files are supported.' });
		}

		// Read file content
		const text = await file.text();
		const lines = text.split(/\r?\n/);

		// Skip header row
		const headerRow = lines[0];
		console.log(headerRow);
		const dataRows = lines.slice(1).filter(line => line.trim() !== '');

		// Process each row
		const results = {
			success: 0,
			errors: 0,
			errorMessages: [] as string[]
		};

		// Prepare data structures for bulk operations
		type ParsedRow = {
			phoneNumber: string;
			submissionDate: Date;
			email: string | null;
			fullName: string;
			gender: string;
			age: Date | null;
			governorate: string;
			educationLevel: string;
			referralSource: string;
			department: string | null;
			specialization: string | null;
			hasPreviousExperience: string;
			artisticStatement: string;
			knowsFineArtMarket: string;
			watchedRikabiEpisodes: string;
			adoptsRikabiPrinciples: string;
			readyForYearCommitment: string;
			paymentPreference: string | null;
			readyForOnSiteTraining: string;
		};

		const parsedRows: ParsedRow[] = [];
		const phoneNumbers: Set<string> = new Set();
		const rowErrors: { index: number; error: string }[] = [];

		// Parse all rows first
		for (let i = 0; i < dataRows.length; i++) {
			try {
				const row = dataRows[i];
				// Parse CSV row (handle quoted values with commas)
				const values: string[] = [];
				let currentValue = '';
				let inQuotes = false;

				for (let j = 0; j < row.length; j++) {
					const char = row[j];

					if (char === '"') {
						if (inQuotes && j + 1 < row.length && row[j + 1] === '"') {
							// Handle escaped quotes
							currentValue += '"';
							j++; // Skip the next quote
						} else {
							// Toggle quote state
							inQuotes = !inQuotes;
						}
					} else if (char === ',' && !inQuotes) {
						// End of value
						values.push(currentValue);
						currentValue = '';
					} else {
						currentValue += char;
					}
				}

				// Add the last value
				values.push(currentValue);

				// Extract data from parsed values
				const [
					phoneNumber,
					submissionDateStr,
					email,
					fullName,
					gender,
					ageStr,
					governorate,
					educationLevel,
					referralSource,
					department,
					specialization,
					hasPreviousExperience,
					artisticStatement,
					knowsFineArtMarket,
					watchedRikabiEpisodes,
					adoptsRikabiPrinciples,
					readyForYearCommitment,
					paymentPreference,
					readyForOnSiteTraining
				] = values;

				// Validate required fields
				if (!phoneNumber || !fullName || !gender || !governorate || !educationLevel || !referralSource) {
					throw new Error('Missing required fields');
				}

				// Parse submission date
				let submissionDate = new Date();
				if (submissionDateStr) {
					try {
						submissionDate = new Date(submissionDateStr);
					} catch (e) {
						// Use the current date if parsing fails
						console.warn(`Error parsing submission date: ${e}`);
					}
				}

				// Parse age (now a date of birth)
				let age: Date | null = null;
				if (ageStr) {
					try {
						age = new Date(ageStr);
						// Validate that it's a valid date
						if (isNaN(age.getTime())) {
							age = null;
						}
					} catch (e) {
						age = null;
						console.warn(`Error parsing age: ${e}`);
					}
				}

				// Add to parsed rows
				parsedRows.push({
					phoneNumber,
					submissionDate,
					email: email || null,
					fullName,
					gender,
					age,
					governorate,
					educationLevel,
					referralSource,
					department: department || null,
					specialization: specialization || null,
					hasPreviousExperience,
					artisticStatement,
					knowsFineArtMarket,
					watchedRikabiEpisodes,
					adoptsRikabiPrinciples,
					readyForYearCommitment,
					paymentPreference: paymentPreference || null,
					readyForOnSiteTraining
				});

				// Add phone number to set for bulk lookup
				phoneNumbers.add(phoneNumber);
			} catch (error) {
				rowErrors.push({
					index: i,
					error: error instanceof Error ? error.message : 'Unknown error'
				});
			}
		}

		// Process in batches
		const BATCH_SIZE = 1000; // Adjust based on database capabilities

		try {
			// Step 1: Find all existing users in bulk
			// Process phone numbers in batches to avoid too many parameters in the query
			const existingUsers: typeof subscriberUser.$inferSelect[] = [];
			const phoneNumbersArray = Array.from(phoneNumbers);

			for (let i = 0; i < phoneNumbersArray.length; i += BATCH_SIZE) {
				const phoneBatch = phoneNumbersArray.slice(i, i + BATCH_SIZE);
				const batchUsers = await db
					.select()
					.from(subscriberUser)
					.where(in_(subscriberUser.phoneNumber, phoneBatch));
				existingUsers.push(...batchUsers);
			}

			// Create a map of phone numbers to user IDs
			const phoneToUserId = new Map(
				existingUsers.map(user => [user.phoneNumber, user.id])
			);

			// Step 2: Prepare new users to be created
			const newUserPhones = Array.from(phoneNumbers).filter(
				phone => !phoneToUserId.has(phone)
			);

			// Step 3: Create new users in batches
			for (let i = 0; i < newUserPhones.length; i += BATCH_SIZE) {
				const batch = newUserPhones.slice(i, i + BATCH_SIZE);
				const newUserValues = batch.map(phoneNumber => ({ phoneNumber }));

				if (newUserValues.length > 0) {
					await db.insert(subscriberUser).values(newUserValues);
				}
			}

			// Step 4: Get all users (including newly created ones)
			const allUsers: typeof subscriberUser.$inferSelect[] = [];

			// Process in batches to avoid too many parameters
			for (let i = 0; i < phoneNumbersArray.length; i += BATCH_SIZE) {
				const phoneBatch = phoneNumbersArray.slice(i, i + BATCH_SIZE);
				const batchUsers = await db
					.select()
					.from(subscriberUser)
					.where(in_(subscriberUser.phoneNumber, phoneBatch));
				allUsers.push(...batchUsers);
			}

			// Update the map with all users
			const updatedPhoneToUserId = new Map(
				allUsers.map(user => [user.phoneNumber, user.id])
			);

			// Step 5: Prepare art registrations
			const artRegistrationValues: (typeof artRegistrations.$inferInsert)[] = [];
			for (const row of parsedRows) {
				const userId = updatedPhoneToUserId.get(row.phoneNumber);
				if (!userId) {
					results.errorMessages.push(
						`User ID not found for phone number ${row.phoneNumber}. Skipping art registration for this row.`
					);
					results.errors++;
				} else {
					artRegistrationValues.push({
						userId: userId,
						submissionDate: row.submissionDate,
						email: row.email,
						fullName: row.fullName,
						gender: row.gender as typeof GENDER[number],
						dateOfBirth: row.age, // Assuming row.age is a timestamp for dateOfBirth
						governorate: row.governorate as typeof GOVERNORATES[number],
						educationLevel: row.educationLevel as typeof EDUCATION_LEVELS[number],
						referralSource: row.referralSource as typeof REFERRAL_SOURCES[number],
						department: row.department,
						specialization: row.specialization,
						hasPreviousExperience: row.hasPreviousExperience as typeof YES_NO[number],
						artisticStatement: row.artisticStatement,
						knowsFineArtMarket: row.knowsFineArtMarket as typeof YES_NO[number],
						watchedRikabiEpisodes: row.watchedRikabiEpisodes as typeof YES_NO[number],
						adoptsRikabiPrinciples: row.adoptsRikabiPrinciples as typeof YES_NO[number],
						readyForYearCommitment: row.readyForYearCommitment as typeof YES_NO[number],
						paymentPreference: row.paymentPreference as typeof PAYMENT_PREFERENCE[number],
						readyForOnSiteTraining: row.readyForOnSiteTraining as typeof YES_NO[number]
					});
				}
			}

			// Step 6: Insert art registrations in batches
			for (let i = 0; i < artRegistrationValues.length; i += BATCH_SIZE) {
				const batch = artRegistrationValues.slice(i, i + BATCH_SIZE);

				if (batch.length > 0) {
					await db.insert(artRegistrations).values(batch);
				}

				results.success += batch.length;
			}

			// Add row errors to results
			results.errors += rowErrors.length; // Append count of parsing errors
			results.errorMessages.push(...rowErrors.map(err => `Row ${err.index + 1} (parsing): ${err.error}`)); // Append parsing error messages

		} catch (error) {
			// Handle any errors during bulk operations
			return fail(500, { 
				success: false, 
				message: `Error during bulk operations: ${error instanceof Error ? error.message : 'Unknown error'}` 
			});
		}

		return {
			success: true,
			imported: results.success,
			errors: results.errors,
			errorMessages: results.errorMessages
		};
	},

	exportData: async ({ request }) => {
		await requireAdminLogin();

		// Get all registrations without pagination, joined with subscriber user to get phone number
		const registrationsWithPhoneNumber = await db
			.select({
				registration: artRegistrations,
				phoneNumber: subscriberUser.phoneNumber
			})
			.from(artRegistrations)
			.leftJoin(subscriberUser, eq(artRegistrations.userId, subscriberUser.id))
			.orderBy(sql`${artRegistrations.createdAt}
			DESC`);

		// Define CSV headers based on the schema
		const headers = [
			'Phone Number',
			'Submission Date',
			'Email',
			'Full Name',
			'Gender',
			'Date of Birth',
			'Governorate',
			'Education Level',
			'Referral Source',
			'Department',
			'Specialization',
			'Has Previous Experience',
			'Artistic Statement',
			'Knows Fine Art Market',
			'Watched Rikabi Episodes',
			'Adopts Rikabi Principles',
			'Ready For Year Commitment',
			'Payment Preference',
			'Ready For On-Site Training'
		];

		// Convert registrations to CSV rows
		const rows = registrationsWithPhoneNumber.map((item) => [
			item.phoneNumber || '',
			item.registration.submissionDate
				? new Date(item.registration.submissionDate).toISOString()
				: '',
			item.registration.email || '',
			item.registration.fullName,
			item.registration.gender,
			item.registration.dateOfBirth ? new Date(item.registration.dateOfBirth).toISOString().split('T')[0] : '',
			item.registration.governorate,
			item.registration.educationLevel,
			item.registration.referralSource,
			item.registration.department || '',
			item.registration.specialization || '',
			item.registration.hasPreviousExperience,
			item.registration.artisticStatement,
			item.registration.knowsFineArtMarket,
			item.registration.watchedRikabiEpisodes,
			item.registration.adoptsRikabiPrinciples,
			item.registration.readyForYearCommitment,
			item.registration.paymentPreference || '',
			item.registration.readyForOnSiteTraining
		]);

		// Create CSV content
		const csvContent = [
			headers.join(','),
			...rows.map((row) =>
				row
					.map((cell) =>
						// Escape quotes and wrap in quotes if contains comma or newline
						typeof cell === 'string' &&
						(cell.includes(',') || cell.includes('\n') || cell.includes('"'))
							? `"${cell.replace(/"/g, '""')}"`
							: cell
					)
					.join(',')
			)
		].join('\n');

		// Return CSV data as a plain object
		return {
			success: true,
			csvData: csvContent,
			filename: `art-registrations-${new Date().toISOString().split('T')[0]}.csv`
		};
	}
};
