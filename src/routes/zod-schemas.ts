import z from 'zod';

// Define enums to match database schema
export const GENDER = ['ذكر', 'أنثى'] as const;
export const YES_NO = ['نعم', 'لا'] as const;
export const PAYMENT_PREFERENCE = [
	'نعم، كاملا 300 الف دينار عراقي اشتراك سنوي',
	'نعم، اشتراك نصف سنوي 150 الف دينار عراقي',
	'شهري',
	'لا، ليس لدي المقدرة على الدفع'
] as const;
export const GOVERNORATES = [
	'بغداد',
	'نينوى',
	'البصرة',
	'أربيل',
	'السليمانية',
	'ذي قار',
	'الأنبار',
	'بابل',
	'كربلاء',
	'النجف',
	'صلاح الدين',
	'واسط',
	'ديالى',
	'دهوك',
	'المثنى',
	'ميسان',
	'القادسية',
	'كركوك'
] as const;
export const EDUCATION_LEVELS = [
	'ابتدائية',
	'متوسطة',
	'إعدادية',
	'دبلوم',
	'بكالوريوس',
	'ماجستير',
	'دكتوراه',
	'أمي'
] as const;
export const REFERRAL_SOURCES = [
	'فيسبوك',
	'انستغرام',
	'تويتر',
	'تيك توك',
	'يوتيوب',
	'صديق',
	'قريب',
	'إعلان',
	'البريد الإلكتروني',
	'بحث على الإنترنت',
	'آخر'
] as const;
export const SPECIALIZATION = [
	'الإخراج',
	'الإدارة الفنية',
	'الأزياء',
	'الإنتاج',
	'الأنميشن',
	'التسويق والتوزيع',
	'التشكيل',
	'التصوير',
	'التمثيل',
	'الصوت',
	'الكتابة',
	'المكياج',
	'الموسيقى',
	'المونتاج',
	'صناعة المحتوى',
	'غرفة الحسين (الفنون الدينية)'
] as const;

// Define Zod schema with strict validation
export const subscriberSchema = z
	.object({
		email: z.string().email('البريد الإلكتروني غير صالح').optional().or(z.literal('')),

		fullName: z.string().min(1, 'الاسم الكامل مطلوب').max(100, 'الاسم طويل جداً'),

		gender: z.enum(GENDER, {
			required_error: 'الجنس مطلوب',
			invalid_type_error: 'اختيار غير صالح للجنس'
		}),

		dateOfBirth: z.string().optional(), // Renamed from 'age'

		governorate: z.enum(GOVERNORATES, {
			required_error: 'المحافظة مطلوبة',
			invalid_type_error: 'اختيار غير صالح للمحافظة'
		}),

		educationLevel: z.enum(EDUCATION_LEVELS, {
			required_error: 'المستوى التعليمي مطلوب',
			invalid_type_error: 'اختيار غير صالح للمستوى التعليمي'
		}),

		referralSource: z.enum(REFERRAL_SOURCES, {
			required_error: 'مصدر التعرف على البرنامج مطلوب',
			invalid_type_error: 'اختيار غير صالح لمصدر التعرف'
		}),

		department: z.string().max(100, 'اسم القسم طويل جداً').optional().or(z.literal('')),

		specialization: z.string().max(100, 'التخصص طويل جداً').optional().or(z.literal('')),

		hasPreviousExperience: z.enum(YES_NO, {
			required_error: 'حقل الخبرة السابقة مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		}),

		artisticStatement: z
			.string()
			.min(10, 'البيان الفني قصير جداً (10 أحرف على الأقل)')
			.max(1000, 'البيان الفني طويل جداً (1000 حرف كحد أقصى)'),

		knowsFineArtMarket: z.enum(YES_NO, {
			required_error: 'حقل المعرفة بسوق الفنون مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		}),

		watchedRikabiEpisodes: z.enum(YES_NO, {
			required_error: 'حقل متابعة حلقات الريكابي مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		}),

		adoptsRikabiPrinciples: z.enum(YES_NO, {
			required_error: 'حقل تطبيق مبادئ الريكابي مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		}),

		readyForYearCommitment: z.enum(YES_NO, {
			required_error: 'حقل الالتزام لمدة سنة مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		}),

		paymentPreference: z
			.enum(PAYMENT_PREFERENCE, {
				invalid_type_error: 'اختيار غير صالح لطريقة الدفع'
			})
			.optional(),

		readyForOnSiteTraining: z.enum(YES_NO, {
			required_error: 'حقل التدريب الحضوري مطلوب',
			invalid_type_error: 'إجابة غير صالحة'
		})
	})
	.refine(
		(data) => {
			// Additional validation: If payment preference is required when commitment is "نعم"
			if (data.readyForYearCommitment === 'نعم' && !data.paymentPreference) {
				return false;
			}
			return true;
		},
		{
			message: 'طريقة الدفع مطلوبة في حالة الالتزام لمدة سنة',
			path: ['paymentPreference']
		}
	);

export type SubscriberSchema = typeof subscriberSchema;

// Subscription schema for validation
export const subscriptionSchema = z
	.object({
		id: z.string().optional(), // Optional for creation, required for updates
		userId: z.string().min(1, 'معرف المستخدم مطلوب'),
		currentPeriodStart: z.string().or(z.literal('')),
		currentPeriodEnd: z.string().or(z.literal(''))
	})
	.refine(
		(data) => {
			// Ensure end date is after start date
			return new Date(data.currentPeriodEnd) > new Date(data.currentPeriodStart);
		},
		{
			message: 'يجب أن يكون تاريخ نهاية الاشتراك بعد تاريخ البداية',
			path: ['currentPeriodEnd']
		}
	);

export type SubscriptionSchema = typeof subscriptionSchema;

// Announcement schema for validation
export const announcementSchema = z.object({
	title: z.string().min(1, 'عنوان الإعلان مطلوب').max(200, 'العنوان طويل جداً'),
	content: z.string().min(1, 'محتوى الإعلان مطلوب').max(2000, 'المحتوى طويل جداً'),
	userId: z.string().optional().or(z.literal('')),

	// Targeting fields - all optional
	targetGender: z.enum(GENDER).optional().or(z.literal('')),
	targetGovernorate: z.enum(GOVERNORATES).optional().or(z.literal('')),
	targetEducationLevel: z.enum(EDUCATION_LEVELS).optional().or(z.literal('')),
	targetReferralSource: z.enum(REFERRAL_SOURCES).optional().or(z.literal('')),
	targetDepartment: z.enum(SPECIALIZATION).optional().or(z.literal('')),
	targetSpecialization: z.enum(SPECIALIZATION).optional().or(z.literal('')),
	targetHasPreviousExperience: z.enum(YES_NO).optional().or(z.literal('')),
	targetKnowsFineArtMarket: z.enum(YES_NO).optional().or(z.literal('')),
	targetWatchedRikabiEpisodes: z.enum(YES_NO).optional().or(z.literal('')),
	targetAdoptsRikabiPrinciples: z.enum(YES_NO).optional().or(z.literal('')),
	targetReadyForYearCommitment: z.enum(YES_NO).optional().or(z.literal('')),
	targetPaymentPreference: z.enum(PAYMENT_PREFERENCE).optional().or(z.literal('')),
	targetReadyForOnSiteTraining: z.enum(YES_NO).optional().or(z.literal(''))
});

export type AnnouncementSchema = typeof announcementSchema;
