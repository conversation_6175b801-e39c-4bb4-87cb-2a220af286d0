<script lang="ts">
  import { Button } from "$lib/components/ui/button/index.js";
  import * as Card from "$lib/components/ui/card/index.js";
  import { Label } from "$lib/components/ui/label/index.js";
  import { Input } from "$lib/components/ui/input/index.js";
  import { enhance } from "$app/forms";

  // Define types for our data
  type PageData = {
    phoneNumber: string;
  };

  type ActionData = {
    otp?: string;
    error?: string;
    errors?: Record<string, string | undefined>;
  };

  let { data, form }: { data: PageData; form: ActionData } = $props();

  let otpValue = $state("");


  function handleOTPInput(event: Event) {
    const target = event.target as HTMLInputElement;
    let value = target.value.replace(/\D/g, ''); // Only allow digits
    
    if (value.length > 6) {
      value = value.slice(0, 6);
    }
    
    otpValue = value;
    target.value = value;

    // Auto-submit when OTP is complete
    if (value.length === 6) {
      const form = document.getElementById('otp-form') as HTMLFormElement;
      if (form) {
        form.requestSubmit();
      }
    }
  }
</script>

<div class="flex min-h-screen items-center justify-center p-4">
  <Card.Root class="w-full max-w-md">
    <Card.Header class="space-y-1 text-center">
      <Card.Title class="text-2xl">تحقق من هاتفك</Card.Title>
      <Card.Description>
        لقد أرسلنا رمز تحقق مكون من 6 أرقام إلى<br>
        <strong>{data.phoneNumber}</strong>
      </Card.Description>
    </Card.Header>
    <Card.Content class="space-y-6">
      <form id="otp-form" method="POST" use:enhance class="space-y-6">
        <div class="space-y-4">
          <Label for="otp" class="text-center block">أدخل رمز التحقق</Label>
          
          <div class="flex justify-center">
            <input hidden value={data.phoneNumber} name="phoneNumber" />
            <Input
              id="otp"
              name="otp"
              type="text"
              inputmode="numeric"
              pattern="[0-9]*"
              maxlength={6}
              placeholder="ادخل الرمز"
              class="w-32 text-center text-lg font-mono tracking-widest"
              value={otpValue}
              oninput={handleOTPInput}
            />
          </div>
        </div>

        {#if form?.error}
          <p class="text-sm text-red-500 text-center">{form.error}</p>
        {/if}

        {#if form?.errors?.otp}
          <p class="text-sm text-red-500 text-center">{form.errors.otp}</p>
        {/if}

        <Button type="submit" class="w-full" disabled={otpValue.length !== 6}>
          تأكيد الرمز
        </Button>
      </form>
    </Card.Content>
  </Card.Root>
</div>
