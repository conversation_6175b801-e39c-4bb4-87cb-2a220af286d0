import { db } from '$lib/server/db';
import { artRegistrations } from '$lib/server/db/schema';
import { fail, redirect } from '@sveltejs/kit';
import { zod } from 'sveltekit-superforms/adapters';
import { message, superValidate } from 'sveltekit-superforms/server';
import type { Actions, PageServerLoad } from './$types';
import { subscriberSchema } from '@/routes/zod-schemas';
import { eq } from 'drizzle-orm';

// If the user is already logged in, redirect them to the appropriate dashboard
export const load: PageServerLoad = async ({ locals }) => {
	if (locals.user) {
		const isAdmin = 'email' in locals.user; // Assuming admin has 'email' for differentiation
		if (isAdmin) {
			throw redirect(303, '/admin');
		} else {
			// User is logged in and is a potential subscriber
			const registration = await db.query.artRegistrations.findFirst({
				where: eq(artRegistrations.userId, locals.user.id) // Safe: locals.user.id is accessed only if user exists
			});
			if (registration) {
				throw redirect(303, '/subs'); // Already registered, go to subscriber area
			}
			// If not registered, proceed to show the form (user is logged in but no artReg yet)
		}
	}
	// If locals.user is not defined, or if user is logged in but not admin and not registered,
	// proceed to load the form for new registration.
	const form = await superValidate(zod(subscriberSchema));

	return {
		form,
		meta: {
			title: 'تسجيل حساب جديد',
			description: 'انضم إلى مجتمعنا الفني وابدأ رحلتك معنا'
		}
	};
};

export const actions: Actions = {
	default: async (event) => {
		const { request, locals } = event;
		const user = locals.user;

		console.log('Form submission received');
		console.log('User:', user);

		try {
			const form = await superValidate(request, zod(subscriberSchema));

			console.log('Form validation result:', {
				valid: form.valid,
				errors: form.errors,
				data: form.data
			});

			if (!form.valid) {
				console.log('Form validation failed:', form.errors);
				return fail(400, { form });
			}

			// Check if user is authenticated
			if (!user) {
				console.log('User not authenticated');
				return message(
					form,
					{
						type: 'error',
						text: 'يجب تسجيل الدخول لإكمال التسجيل'
					},
					{ status: 401 }
				);
			}

			console.log('Attempting to save registration for user:', user.id);
			console.log('Form data to save:', form.data);

			// Prepare the data for insertion
			const registrationData = {
				userId: user.id,
				submissionDate: new Date(),
				email: form.data.email || null,
				fullName: form.data.fullName,
				gender: form.data.gender,
				dateOfBirth: form.data.dateOfBirth ? new Date(form.data.dateOfBirth) : null, // Renamed from 'age' and converting to Date object
				governorate: form.data.governorate,
				educationLevel: form.data.educationLevel,
				referralSource: form.data.referralSource,
				department: form.data.department || null,
				specialization: form.data.specialization || null,
				hasPreviousExperience: form.data.hasPreviousExperience,
				artisticStatement: form.data.artisticStatement,
				knowsFineArtMarket: form.data.knowsFineArtMarket,
				watchedRikabiEpisodes: form.data.watchedRikabiEpisodes,
				adoptsRikabiPrinciples: form.data.adoptsRikabiPrinciples,
				readyForYearCommitment: form.data.readyForYearCommitment,
				paymentPreference: form.data.paymentPreference || null,
				readyForOnSiteTraining: form.data.readyForOnSiteTraining,
				createdAt: new Date()
			};

			console.log('Registration data prepared:', registrationData);

			// Create art registration
			const result = await db.insert(artRegistrations).values(registrationData);

			console.log('Registration saved successfully:', result);
		} catch (error) {
			console.error('Registration error:', error);
			
			// If it's a redirect, re-throw it
			if (error instanceof Response) {
				throw error;
			}
			
			// Create a new form for error response
			const form = await superValidate(request, zod(subscriberSchema));
			
			// Return a user-friendly error message
			return message(
				form,
				{
					type: 'error',
					text: `حدث خطأ أثناء التسجيل: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
				},
				{ status: 500 }
			);
		}
		return redirect(303, '/subs');
	}
};
