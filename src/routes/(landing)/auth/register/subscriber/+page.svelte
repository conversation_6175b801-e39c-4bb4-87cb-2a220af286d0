<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import type { PageData } from './$types.js';
	import { superForm } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import {
		subscriberSchema,
		GENDER,
		GOVERNORATES,
		EDUCATION_LEVELS,
		REFERRAL_SOURCES,
		YES_NO,
		PAYMENT_PREFERENCE,
		SPECIALIZATION
	} from '@/routes/zod-schemas';
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Select from '$lib/components/ui/select/index.js';

	import type { z } from 'zod';


	let { data }: { data: PageData } = $props();

	let { form, submitting, errors, enhance, message } = superForm(data.form, {
		validators: zod(subscriberSchema),
		resetForm: false,
		dataType: 'json',
		errorSelector: '[data-invalid]',
		scrollToError: 'smooth',
		autoFocusOnError: 'detect',
		stickyNavbar: undefined,
		onError({ result }) {
			console.error('Form submission error:', result);
		},
		onResult({ result }) {
			console.log('Form result:', result);
		}
	});


</script>

<div class="flex min-h-screen items-center justify-center p-4">
	<Card.Root class="w-full max-w-4xl">
		<Card.Header class="space-y-1">
			<Card.Title class="text-2xl">نموذج تسجيل الفن</Card.Title>
			<Card.Description>
				أكمل تسجيلك للانضمام إلى برنامجنا الفني. جميع الحقول المميزة بـ * مطلوبة.
			</Card.Description>
		</Card.Header>
		<Card.Content class="grid gap-6">
			<form class="space-y-6" method="POST" use:enhance>
				<!-- Contact Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-semibold">معلومات الاتصال</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="email">
								البريد الإلكتروني
								<span class="text-red-500">(اختياري)</span>
							</Label>
							<Input
								bind:value={$form.email}
								data-invalid={$errors.email}
								id="email"
								name="email"
								placeholder="<EMAIL>"
								type="email"
							/>
							{#if $errors.email}
								<p class="text-sm text-red-600">{$errors.email}</p>
							{/if}
						</div>
					</div>
				</div>

				<!-- Personal Information -->
				<div class="space-y-4">
					<h3 class="text-lg font-semibold">المعلومات الشخصية</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="fullName">
								الاسم الكامل
								<span class="text-red-500">*</span>
							</Label>
							<Input
								bind:value={$form.fullName}
								data-invalid={$errors.fullName}
								id="fullName"
								name="fullName"
								placeholder="الاسم الكامل"
								required
								type="text"
							/>
							{#if $errors.fullName}
								<p class="text-sm text-red-600">{$errors.fullName}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="dateOfBirth">
								تاريخ الميلاد
								<span class="text-red-500">(اختياري)</span>
							</Label>
							<Input
								bind:value={$form.dateOfBirth}
								data-invalid={$errors.dateOfBirth}
								id="dateOfBirth"
								name="dateOfBirth"
								placeholder="YYYY-MM-DD"
								type="date"
							/>
							{#if $errors.dateOfBirth}
								<p class="text-sm text-red-600">{$errors.dateOfBirth}</p>
							{/if}
						</div>
					</div>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="gender">
								الجنس
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root bind:value={$form.gender} name="gender" required type="single">
								<Select.Trigger class="w-full" id="gender">
									{$form.gender || 'اختر الجنس'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each GENDER as genderOption (genderOption)}
											<Select.Item value={genderOption}>
												{genderOption}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.gender}
								<p class="text-sm text-red-600">{$errors.gender}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="governorate">
								المحافظة
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root bind:value={$form.governorate} name="governorate" required type="single">
								<Select.Trigger class="w-full" id="governorate">
									{$form.governorate || 'اختر المحافظة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each GOVERNORATES as governorate (governorate)}
											<Select.Item value={governorate}>
												{governorate}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.governorate}
								<p class="text-sm text-red-600">{$errors.governorate}</p>
							{/if}
						</div>
					</div>
				</div>

				<!-- Education & Background -->
				<div class="space-y-4">
					<h3 class="text-lg font-semibold">المؤهل العلمي والخلفية</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="educationLevel">
								المستوى التعليمي
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.educationLevel}
								name="educationLevel"
								required
								type="single"
							>
								<Select.Trigger class="w-full" id="educationLevel">
									{$form.educationLevel || 'اختر المستوى التعليمي'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each EDUCATION_LEVELS as educationLevel (educationLevel)}
											<Select.Item value={educationLevel}>
												{educationLevel}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.educationLevel}
								<p class="text-sm text-red-600">{$errors.educationLevel}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="referralSource">
								كيف سمعت عنا؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.referralSource}
								name="referralSource"
								required
								type="single"
							>
								<Select.Trigger class="w-full" id="referralSource">
									{$form.referralSource || '	اختر مصدر التعرف علينا'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each REFERRAL_SOURCES as source (source)}
											<Select.Item value={source}>
												{source}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.referralSource}
								<p class="text-sm text-red-600">{$errors.referralSource}</p>
							{/if}
						</div>
					</div>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="department">
								القسم
								<span class="text-red-500">(اختياري)</span>
							</Label>
							<Select.Root
								bind:value={$form.department}
								name="department"
								required
								type="single"
							>
								<Select.Trigger class="w-full" id="department">
									{$form.department || '	اختر القسم'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each SPECIALIZATION as department (department)}
											<Select.Item value={department}>
												{department}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.department}
								<p class="text-sm text-red-600">{$errors.department}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="specialization">
								الفرع
								<span class="text-red-500">(اختياري)</span>
							</Label>
							<Select.Root
								bind:value={$form.specialization}
								name="specialization"
								required
								type="single"
							>
								<Select.Trigger class="w-full" id="specialization">
									{$form.specialization || '	اختر القسم'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each SPECIALIZATION as specialization (specialization)}
											<Select.Item value={specialization}>
												{specialization}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.specialization}
								<p class="text-sm text-red-600">{$errors.specialization}</p>
							{/if}
						</div>
					</div>
				</div>

				<!-- Art Experience -->
				<div class="space-y-4">
					<h3 class="text-lg font-semibold">الخبرة الفنية</h3>

					<div class="grid gap-2">
						<Label for="hasPreviousExperience">
							هل لديك خبرة فنية سابقة؟
							<span class="text-red-500">*</span>
						</Label>
						<Select.Root
							bind:value={$form.hasPreviousExperience}
							name="hasPreviousExperience"
							type="single"
						>
							<Select.Trigger class="w-full">
								{$form.hasPreviousExperience || 'اختر الإجابة'}
							</Select.Trigger>
							<Select.Content dir="rtl">
								<Select.Group>
									{#each YES_NO as option (option)}
										<Select.Item value={option}>
											{option}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
						{#if $errors.hasPreviousExperience}
							<p class="text-sm text-red-600">{$errors.hasPreviousExperience}</p>
						{/if}
					</div>

					<div class="grid gap-2 md:col-span-2">
						<Label for="artisticStatement">
							بيان فني مختصر
							<span class="text-red-500">*</span>
						</Label>
						<Textarea
							bind:value={$form.artisticStatement}
							class="min-h-[100px]"
							data-invalid={$errors.artisticStatement}
							id="artisticStatement"
							name="artisticStatement"
							placeholder="أخبرنا عن نفسك وخبراتك الفنية"
							required
						/>
						<p class="text-muted-foreground text-xs">{$form.artisticStatement?.length || 0}/1000</p>
						{#if $errors.artisticStatement}
							<p class="text-sm text-red-600">{$errors.artisticStatement}</p>
						{/if}
					</div>
				</div>

				<!-- Program Questions -->
				<div class="space-y-4">
					<h3 class="text-lg font-semibold">أسئلة البرنامج</h3>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="knowsFineArtMarket">
								هل لديك معرفة بسوق الفنون الجميلة؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.knowsFineArtMarket}
								name="knowsFineArtMarket"
								type="single"
							>
								<Select.Trigger class="w-full">
									{$form.knowsFineArtMarket || 'اختر الإجابة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each YES_NO as option (option)}
											<Select.Item value={option}>
												{option}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.knowsFineArtMarket}
								<p class="text-sm text-red-600">{$errors.knowsFineArtMarket}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="watchedRikabiEpisodes">
								هل شاهدت الحلقات الثلاث للفنان مصطفى الركابي؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.watchedRikabiEpisodes}
								name="watchedRikabiEpisodes"
								type="single"
							>
								<Select.Trigger class="w-full">
									{$form.watchedRikabiEpisodes || 'اختر الإجابة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each YES_NO as option (option)}
											<Select.Item value={option}>
												{option}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.watchedRikabiEpisodes}
								<p class="text-sm text-red-600">{$errors.watchedRikabiEpisodes}</p>
							{/if}
						</div>
					</div>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							<Label for="adoptsRikabiPrinciples">
								هل تتبنى ما ذكر في الحلقات الثلاث من أسباب ودوافع لتأسيس هذا المشروع؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.adoptsRikabiPrinciples}
								name="adoptsRikabiPrinciples"
								type="single"
							>
								<Select.Trigger class="w-full">
									{$form.adoptsRikabiPrinciples || 'اختر الإجابة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each YES_NO as option (option)}
											<Select.Item value={option}>
												{option}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.adoptsRikabiPrinciples}
								<p class="text-sm text-red-600">{$errors.adoptsRikabiPrinciples}</p>
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="readyForYearCommitment">
								هل أنت مستعد للالتزام بالبرنامج لمدة سنة كاملة؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.readyForYearCommitment}
								name="readyForYearCommitment"
								type="single"
							>
								<Select.Trigger class="w-full">
									{$form.readyForYearCommitment || 'اختر الإجابة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each YES_NO as option (option)}
											<Select.Item value={option}>
												{option}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.readyForYearCommitment}
								<p class="text-sm text-red-600">{$errors.readyForYearCommitment}</p>
							{/if}
						</div>
					</div>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="grid gap-2">
							{#if $form.readyForYearCommitment === 'نعم'}
								<Label for="paymentPreference">
									طريقة الدفع المفضلة
									<span class="text-red-500">*</span>
								</Label>
								<Select.Root
									type="single"
									name="paymentPreference"
									bind:value={$form.paymentPreference}
								>
									<Select.Trigger class="w-full">
										{$form.paymentPreference || 'اختر طريقة الدفع'}
									</Select.Trigger>
									<Select.Content dir="rtl">
										<Select.Group>
											{#each PAYMENT_PREFERENCE as option (option)}
												<Select.Item value={option}>
													{option}
												</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
								{#if $errors.paymentPreference}
									<p class="text-sm text-red-600">{$errors.paymentPreference}</p>
								{/if}
							{/if}
						</div>

						<div class="grid gap-2">
							<Label for="readyForOnSiteTraining">
								هل أنت مستعد للتدريب الحضوري في بغداد؟
								<span class="text-red-500">*</span>
							</Label>
							<Select.Root
								bind:value={$form.readyForOnSiteTraining}
								name="readyForOnSiteTraining"
								type="single"
							>
								<Select.Trigger class="w-full">
									{$form.readyForOnSiteTraining || 'اختر الإجابة'}
								</Select.Trigger>
								<Select.Content dir="rtl">
									<Select.Group>
										{#each YES_NO as option (option)}
											<Select.Item value={option}>
												{option}
											</Select.Item>
										{/each}
									</Select.Group>
								</Select.Content>
							</Select.Root>
							{#if $errors.readyForOnSiteTraining}
								<p class="text-sm text-red-600">{$errors.readyForOnSiteTraining}</p>
							{/if}
						</div>
					</div>
				</div>

				<!-- Success/Error Messages -->
				{#if $message}
					<div
						class="rounded-md p-4 {$message.type === 'error' ? 'bg-red-50 text-red-800' : 'bg-green-50 text-green-800'}">
						<p class="text-sm font-medium">{$message.text}</p>
					</div>
				{/if}

				<Button class="w-full" disabled={$submitting} type="submit">
					{$submitting ? 'جاري التسجيل...' : 'إكمال التسجيل'}
				</Button>
			</form>
		</Card.Content>
	</Card.Root>
</div>
